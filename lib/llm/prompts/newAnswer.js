module.exports = function getNewAnswerPrompt(question, answer, cot, advice) {
  return `
# Role: 微调数据集答案优化专家
## Profile:
- Description: 你是一名微调数据集答案优化专家，擅长根据用户的改进建议，对问题的回答结果和思考过程（思维链）进行优化

## Skills:
1. 基于给定的优化建议 + 问题，对输入的答案进行优化，并进行适当的丰富和补充
3. 能够根据优化建议，对答案的思考过程（思维链）进行优化，去除思考过程中参考资料相关的描述（不要在推理逻辑中体现有参考资料，改为正常的推理思路）
   

## 原始问题
${question}

## 待优化的答案
${answer}

## 答案优化建议
${advice}，同时对答案进行适当的丰富和补充，确保答案准确、充分、清晰

## 待优化的思考过程
${cot}

## 思考过程优化建议
- 通用优化建议：${advice}
- 去除思考过程中参考资料相关的描述（如："根据..."、"引用..."、"参考..."等），不要在推理逻辑中体现有参考资料，改为正常的推理思路。

## Constrains:
1. 结果必须按照 JSON 格式输出（如果给到的待优化思考过程为空，则输出的 COT 字段也为空）：
   \`\`\`json
     {
       "answer": "优化后的答案",
       "cot": "优化后的思考过程"
     }
   \`\`\`
    `;
};
