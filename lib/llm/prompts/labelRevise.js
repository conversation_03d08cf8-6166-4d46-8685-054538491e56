/**
 * 领域树增量修订提示词
 * 用于在已有领域树的基础上，针对新增/删除的文献内容，对领域树进行增量调整
 */
function getLabelRevisePrompt({ text, existingTags, deletedContent, newContent, globalPrompt, domainTreePrompt }) {
  const prompt = `
  
${globalPrompt ? `- 在后续的任务中，你务必遵循这样的规则：${globalPrompt}` : ''}

我需要你帮我修订一个已有的领域树结构，使其能够适应内容的变化。
${domainTreePrompt ? domainTreePrompt : ''}

## 之前的领域树结构
以下是之前完整的领域树结构（JSON格式）：
\`\`\`json
${JSON.stringify(existingTags, null, 2)}
\`\`\`


## 之前完整文献的目录
以下是当前系统中所有文献的目录结构总览：
\`\`\`
${text}
\`\`\`

${
  deletedContent
    ? `## 被删除的内容
以下是本次要删除的文献目录信息：
\`\`\`
${deletedContent}
\`\`\`
`
    : ''
}

${
  newContent
    ? `## 新增的内容
以下是本次新增的文献目录信息：
\`\`\`
${newContent}
\`\`\`
`
    : ''
}

## 要求
请分析上述信息，修订现有的领域树结构，遵循以下原则：
1. 保持领域树的总体结构稳定，避免大规模重构
2. 对于删除的内容相关的领域标签：
   - 如果某个标签仅与删除的内容相关，且在现有文献中找不到相应内容支持，则移除该标签
   - 如果某个标签同时与其他保留的内容相关，则保留该标签
3. 对于新增的内容：
   - 如果新内容可以归类到现有的标签中，优先使用现有标签
   - 如果新内容引入了现有标签体系中没有的新领域或概念，再创建新的标签
4. 每个标签必须对应目录结构中的实际内容，不要创建没有对应内容支持的空标签
5. 确保修订后的领域树仍然符合良好的层次结构，标签间具有合理的父子关系

## 限制
1. 一级领域标签数量5-10个
2. 二级领域标签数量1-10个
3. 最多两层分类层级
4. 分类必须与原始目录内容相关
5. 输出必须符合指定 JSON 格式，不要输出 JSON 外其他任何不相关内容
6. 标签的名字最多不要超过 6 个字
7. 在每个标签前加入序号（序号不计入字数）

## 输出格式
最终输出修订后的完整领域树结构，使用下面的JSON格式：

\`\`\`json
[
  {
    "label": "1 一级领域标签",
    "child": [
      {"label": "1.1 二级领域标签1"},
      {"label": "1.2 二级领域标签2"}
    ]
  },
  {
    "label": "2 一级领域标签(无子标签)"
  }
]
\`\`\`

确保你的回答中只包含JSON格式的领域树，不要有其他解释性文字。`;

  return prompt;
}

module.exports = getLabelRevisePrompt;
