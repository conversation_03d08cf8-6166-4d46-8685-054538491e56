# 🚀 MED-Dataset 启动指南

## 📋 快速开始

### 🎯 **推荐方式：交互式启动**

我们为您提供了智能启动脚本，会自动询问您选择哪种运行模式：

```bash
# 方式1：Node.js 交互式启动（推荐）
npm run start-interactive

# 方式2：Shell 交互式启动（Linux/Mac）
npm run start-menu
```

### 📱 **启动界面预览**

运行交互式启动后，您会看到：

```
🎉 欢迎使用 MED-Dataset！
==================================================

📖 MED-Dataset 是一个强大的医疗领域大型语言模型微调数据集创建工具

🚀 支持两种运行模式：

请选择启动模式：

1️⃣  生产模式 (推荐)
   ✅ 启动速度极快 (187ms)
   ✅ 页面加载迅速 (1-5秒)
   ✅ 运行稳定，内存使用少
   ✅ 适合日常使用数据集工具

2️⃣  开发模式
   🔧 支持代码热重载
   🔧 详细的调试信息
   ⚠️  启动较慢 (2-5分钟)
   ⚠️  适合修改代码时使用

3️⃣  查看详细说明
4️⃣  退出

请输入您的选择 (1-4):
```

## 🎯 **新手推荐**

如果您是第一次使用，建议：

1. **运行交互式启动**：
   ```bash
   npm run start-interactive
   ```

2. **选择生产模式**：输入 `1` 并按回车

3. **等待启动完成**：大约3-5秒

4. **打开浏览器**：访问 `http://localhost:1717`

## 📊 **模式对比**

| 使用场景 | 推荐模式 | 启动时间 | 页面加载 |
|---------|---------|---------|---------|
| 📊 日常使用数据集工具 | 生产模式 | 187ms | 1-5秒 |
| 🔧 修改代码/自定义功能 | 开发模式 | 2-5分钟 | 30-60秒 |
| ⚡ 追求最佳性能 | 生产模式 | 187ms | 1-5秒 |
| 🐛 调试问题 | 开发模式 | 2-5分钟 | 30-60秒 |

## 🔧 **传统启动方式**

如果您已经知道要使用哪种模式：

### 生产模式（推荐）
```bash
# 首次使用或代码更新后需要构建
npm run build

# 启动生产服务器
npm run start
```

### 开发模式
```bash
# 直接启动开发服务器
npm run dev
```

## 💡 **使用技巧**

### 🌟 **首次使用建议**
1. 使用交互式启动：`npm run start-interactive`
2. 选择生产模式（输入 `1`）
3. 等待启动完成
4. 开始创建您的第一个数据集项目

### 🔄 **模式切换**
- **从开发模式切换到生产模式**：
  1. 停止当前服务器（Ctrl+C）
  2. 运行 `npm run build`
  3. 运行 `npm run start`

- **从生产模式切换到开发模式**：
  1. 停止当前服务器（Ctrl+C）
  2. 运行 `npm run dev`

### 📈 **性能优化建议**
- **日常使用**：始终选择生产模式
- **代码开发**：仅在需要修改代码时使用开发模式
- **长时间运行**：生产模式更稳定，不会出现内存重启

## ❓ **常见问题**

### Q: 交互式启动失败怎么办？
**A**: 确保您在项目根目录下，并且已经安装了依赖：
```bash
npm install
npm run start-interactive
```

### Q: 生产模式需要每次构建吗？
**A**: 不需要。只有在以下情况需要重新构建：
- 首次使用
- 代码有更新
- 删除了 `.next` 目录

### Q: 如何知道应用启动成功？
**A**: 看到以下信息表示启动成功：
```
▲ Next.js 14.2.29
- Local:        http://localhost:1717
✓ Ready in 187ms
```

### Q: 端口被占用怎么办？
**A**: 检查是否有其他应用使用了1717端口，或者修改启动命令中的端口号。

## 🎉 **开始使用**

现在您可以：

1. **运行交互式启动**：
   ```bash
   npm run start-interactive
   ```

2. **选择合适的模式**

3. **打开浏览器访问**：`http://localhost:1717`

4. **开始创建您的数据集项目**！

---

💡 **提示**：建议将此文档保存为书签，方便随时查看启动方式。

🔗 **更多信息**：查看 `启动模式选择指南.md` 了解详细的技术对比。
