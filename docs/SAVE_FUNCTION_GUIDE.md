# MED-Dataset 保存功能实现指南

## 概述

本文档详细介绍了MED-Dataset医疗数据集生成与管理平台中任务配置页面的保存功能实现。该功能为用户提供了完整的配置保存体验，包括实时状态反馈、数据验证、错误处理等。

## 功能特点

### 1. 用户体验优化
- **吸底设计**: 保存按钮固定在页面底部，用户滚动时始终可见
- **加载状态**: 保存过程中显示加载动画，防止重复提交
- **视觉反馈**: 按钮悬停效果和状态变化动画
- **响应式设计**: 适配不同屏幕尺寸

### 2. 数据验证
- **参数验证**: 保存前验证配置参数的有效性
- **类型检查**: 确保数据类型正确
- **范围验证**: 检查数值参数是否在合理范围内

### 3. 错误处理
- **详细错误信息**: 提供具体的错误描述
- **自动隐藏**: 错误提示5秒后自动消失
- **用户友好**: 错误信息本地化显示

### 4. 成功反馈
- **即时确认**: 保存成功后立即显示确认提示
- **自动隐藏**: 成功提示3秒后自动消失
- **状态同步**: 更新应用状态

## 技术实现

### 1. 核心Hook: useTaskSettings

```javascript
// hooks/useTaskSettings.js
export default function useTaskSettings(projectId) {
  const [taskSettings, setTaskSettings] = useState({...DEFAULT_SETTINGS});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [saving, setSaving] = useState(false);

  // 保存任务配置函数
  const saveTaskSettings = async (settingsToSave = taskSettings) => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(false);

      // 数据验证
      if (!settingsToSave.chunkSize || settingsToSave.chunkSize < 100) {
        throw new Error('块大小不能小于100字符');
      }

      // API调用
      const response = await fetch(`/api/projects/${projectId}/tasks`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settingsToSave)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '保存失败');
      }

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
      return true;
      
    } catch (error) {
      setError(error.message);
      setTimeout(() => setError(null), 5000);
      return false;
    } finally {
      setSaving(false);
    }
  };

  return {
    taskSettings,
    setTaskSettings,
    loading,
    error,
    success,
    setSuccess,
    saving,
    saveTaskSettings
  };
}
```

### 2. 组件实现: TaskSettings

```javascript
// components/settings/TaskSettings.js
export default function TaskSettings({ projectId }) {
  const { 
    taskSettings, 
    setTaskSettings, 
    loading, 
    error, 
    success, 
    setSuccess, 
    saving, 
    saveTaskSettings 
  } = useTaskSettings(projectId);

  const handleSaveTaskSettings = async () => {
    await saveTaskSettings();
  };

  return (
    <Box sx={{ position: 'relative', pb: 8 }}>
      {/* 配置表单内容 */}
      
      {/* 吸底保存按钮 */}
      <Box sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        padding: '8px',
        backgroundColor: 'background.paper',
        borderTop: '1px solid',
        borderColor: 'divider',
        zIndex: 1100,
        display: 'flex',
        justifyContent: 'center',
        boxShadow: 3
      }}>
        <Button
          variant="contained"
          color="primary"
          size="medium"
          startIcon={saving ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
          onClick={handleSaveTaskSettings}
          disabled={saving}
          sx={{
            minWidth: '160px',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: 4
            }
          }}
        >
          {saving ? '保存中...' : '保存任务配置'}
        </Button>
      </Box>

      {/* 成功提示 */}
      <Snackbar open={success} autoHideDuration={3000}>
        <Alert severity="success">保存成功</Alert>
      </Snackbar>

      {/* 错误提示 */}
      <Snackbar open={!!error} autoHideDuration={5000}>
        <Alert severity="error">{error}</Alert>
      </Snackbar>
    </Box>
  );
}
```

## 配置项说明

### 文本分割设置
- **最小长度**: 文本块的最小字符数 (默认: 1500)
- **最大分割长度**: 文本块的最大字符数 (默认: 2000)
- **分块策略**: 支持多种分割策略
  - Markdown结构分块
  - 递归字符分块
  - 固定长度分块 (字符/Token)
  - 程序代码智能分块

### 问题生成设置
- **问题生成长度**: 生成问题的字符数范围
- **问题掩码移除概率**: 问题处理的概率参数
- **并发限制**: 同时处理的任务数量

### PDF文件转换配置
- **MinerU Token**: PDF转换API的访问令牌
- **视觉模型并发数**: 自定义视觉模型的并发处理数量

## 使用方法

1. **访问设置页面**: 在项目详情页面点击"设置"选项卡
2. **选择任务配置**: 点击"任务配置"子选项卡
3. **调整参数**: 使用滑块和输入框调整各项配置
4. **保存配置**: 点击页面底部的"保存任务配置"按钮
5. **确认结果**: 查看保存成功或失败的提示信息

## 最佳实践

### 1. 参数配置建议
- **文本分割**: 根据文档类型选择合适的分割策略
- **并发控制**: 根据系统性能调整并发数量
- **Token配置**: 及时更新过期的API令牌

### 2. 错误处理
- **网络错误**: 检查网络连接和API可用性
- **参数错误**: 确保所有必填参数都已正确填写
- **权限错误**: 验证API令牌的有效性

### 3. 性能优化
- **批量保存**: 避免频繁的单项保存操作
- **参数验证**: 在客户端进行基础验证，减少服务器负载
- **状态管理**: 合理使用React状态，避免不必要的重渲染

## 故障排除

### 常见问题
1. **保存失败**: 检查网络连接和参数有效性
2. **加载缓慢**: 检查服务器响应时间
3. **配置丢失**: 确认保存操作已成功完成

### 调试方法
1. **浏览器控制台**: 查看JavaScript错误信息
2. **网络面板**: 检查API请求和响应
3. **React DevTools**: 查看组件状态变化

## 扩展功能

### 1. 自动保存
可以添加定时自动保存功能，避免配置丢失：

```javascript
useEffect(() => {
  const autoSave = setInterval(() => {
    if (hasUnsavedChanges) {
      saveTaskSettings();
    }
  }, 30000); // 30秒自动保存

  return () => clearInterval(autoSave);
}, [hasUnsavedChanges, saveTaskSettings]);
```

### 2. 配置历史
记录配置变更历史，支持回滚操作：

```javascript
const [configHistory, setConfigHistory] = useState([]);

const saveWithHistory = async (newSettings) => {
  const success = await saveTaskSettings(newSettings);
  if (success) {
    setConfigHistory(prev => [...prev, {
      timestamp: new Date(),
      settings: newSettings
    }]);
  }
};
```

## 总结

MED-Dataset的保存功能通过完善的状态管理、用户友好的界面设计和健壮的错误处理，为用户提供了优秀的配置管理体验。该实现可以作为其他类似功能的参考模板。
