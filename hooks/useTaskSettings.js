import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { DEFAULT_SETTINGS } from '@/constant/setting';

export default function useTaskSettings(projectId) {
  const { t } = useTranslation();
  const [taskSettings, setTaskSettings] = useState({
    ...DEFAULT_SETTINGS
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    async function fetchTaskSettings() {
      try {
        setLoading(true);
        const response = await fetch(`/api/projects/${projectId}/tasks`);
        if (!response.ok) {
          throw new Error(t('settings.fetchTasksFailed'));
        }

        const data = await response.json();

        // 如果没有配置，使用默认值
        if (Object.keys(data).length === 0) {
          setTaskSettings({
            ...DEFAULT_SETTINGS
          });
        } else {
          setTaskSettings({
            ...DEFAULT_SETTINGS,
            ...data
          });
        }
      } catch (error) {
        console.error('获取任务配置出错:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    }

    fetchTaskSettings();
  }, [projectId, t]);

  // 保存任务配置函数
  const saveTaskSettings = async (settingsToSave = taskSettings) => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(false);

      // 数据验证 - 确保数值类型
      console.log('Settings to save:', settingsToSave);

      // 验证文本分割最小长度
      if (settingsToSave.textSplitMinLength !== undefined) {
        const minLength = Number(settingsToSave.textSplitMinLength);
        console.log('Validating textSplitMinLength:', minLength, 'type:', typeof minLength);

        if (!minLength || isNaN(minLength) || minLength < 100) {
          throw new Error('最小长度不能小于100字符');
        }

        if (minLength > 10000) {
          throw new Error('最小长度不能大于10000字符');
        }

        settingsToSave.textSplitMinLength = minLength;
      }

      // 验证文本分割最大长度
      if (settingsToSave.textSplitMaxLength !== undefined) {
        const maxLength = Number(settingsToSave.textSplitMaxLength);
        console.log('Validating textSplitMaxLength:', maxLength, 'type:', typeof maxLength);

        if (!maxLength || isNaN(maxLength) || maxLength < 1000) {
          throw new Error('最大长度不能小于1000字符');
        }

        if (maxLength > 20000) {
          throw new Error('最大长度不能大于20000字符');
        }

        settingsToSave.textSplitMaxLength = maxLength;
      }

      // 验证块大小（如果存在）
      if (settingsToSave.chunkSize !== undefined) {
        const chunkSize = Number(settingsToSave.chunkSize);
        if (!chunkSize || isNaN(chunkSize) || chunkSize < 500) {
          throw new Error('块大小不能小于500字符');
        }
        if (chunkSize > 10000) {
          throw new Error('块大小不能大于10000字符');
        }
        settingsToSave.chunkSize = chunkSize;
      }

      // 验证重叠长度（如果存在）
      if (settingsToSave.chunkOverlap !== undefined) {
        const chunkOverlap = Number(settingsToSave.chunkOverlap);
        if (chunkOverlap < 0 || isNaN(chunkOverlap)) {
          throw new Error('重叠长度不能为负数');
        }
        settingsToSave.chunkOverlap = chunkOverlap;
      }

      // 确保递归分块的分隔符数组存在
      if (settingsToSave.splitType === 'recursive' && settingsToSave.separatorsInput) {
        if (!settingsToSave.separators || !Array.isArray(settingsToSave.separators)) {
          settingsToSave.separators = settingsToSave.separatorsInput.split(',').map(item => item.trim());
        }
      }

      console.log('Saving settings:', settingsToSave);

      const response = await fetch(`/api/projects/${projectId}/tasks`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settingsToSave)
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        let errorMessage = '保存失败';
        try {
          const errorData = await response.json();
          console.log('Error data:', errorData);
          errorMessage = errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`;
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      setSuccess(true);

      // 自动隐藏成功提示
      setTimeout(() => {
        setSuccess(false);
      }, 3000);

      return true;

    } catch (error) {
      console.error('保存任务配置失败:', error);
      setError(error.message);

      // 自动隐藏错误提示
      setTimeout(() => {
        setError(null);
      }, 5000);

      return false;
    } finally {
      setSaving(false);
    }
  };

  return {
    taskSettings,
    setTaskSettings,
    loading,
    error,
    success,
    setSuccess,
    saving,
    saveTaskSettings
  };
}
