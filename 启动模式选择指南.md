# MED-Dataset 启动模式选择指南

## 📖 概述

MED-Dataset 支持两种运行模式：**开发模式**和**生产模式**。本指南将帮助您了解两种模式的区别，并选择最适合您需求的启动方式。

## 🚀 快速启动

### 🎯 **推荐方式：交互式启动**

我们提供了两种交互式启动方式，会自动询问您选择哪种模式：

#### 方式1：Node.js 脚本（推荐）
```bash
npm run start-interactive
```

#### 方式2：Shell 脚本（Linux/Mac）
```bash
npm run start-menu
# 或者直接运行
./start.sh
```

### 📋 **传统启动方式**

如果您已经知道要使用哪种模式，也可以直接启动：

### 第一步：选择运行模式

在启动应用前，请根据您的使用场景选择合适的模式：

#### 🎯 **我应该选择哪种模式？**

| 使用场景 | 推荐模式 | 启动命令 |
|---------|---------|---------|
| 📊 **日常使用数据集工具** | 生产模式 | `npm run start` |
| 🔧 **修改代码/自定义功能** | 开发模式 | `npm run dev` |
| ⚡ **追求最佳性能** | 生产模式 | `npm run start` |
| 🐛 **调试问题** | 开发模式 | `npm run dev` |

### 第二步：启动应用

#### 🌟 **生产模式启动（推荐）**

```bash
# 首次使用或代码有更新时需要构建
npm run build

# 启动生产服务器
npm run start
```

#### 🔧 **开发模式启动**

```bash
# 直接启动开发服务器
npm run dev
```

## 📊 详细对比

### 🎯 **功能对比**

| 功能特性 | 开发模式 | 生产模式 | 说明 |
|---------|---------|---------|------|
| 项目管理 | ✅ | ✅ | 创建、删除、管理项目 |
| 文件上传 | ✅ | ✅ | 支持PDF、DOCX、Markdown等 |
| 文本分割 | ✅ | ✅ | 智能文本分割算法 |
| 问题生成 | ✅ | ✅ | AI自动生成问题 |
| 答案生成 | ✅ | ✅ | LLM生成答案 |
| 数据集导出 | ✅ | ✅ | 多种格式导出 |
| 模型配置 | ✅ | ✅ | 配置各种LLM模型 |
| 模型测试 | ✅ | ✅ | 测试模型对话 |
| **代码热重载** | ✅ | ❌ | 修改代码自动刷新 |

> **重要提示**：除了代码热重载功能外，两种模式的所有核心功能完全相同！

### ⚡ **性能对比**

| 性能指标 | 开发模式 | 生产模式 | 改善幅度 |
|---------|---------|---------|---------|
| **启动时间** | 2-5分钟 | 187毫秒 | 🚀 99%+ |
| **页面加载** | 30-60秒 | 1-5秒 | 🚀 95%+ |
| **API响应** | 6-42秒 | 1-3秒 | 🚀 90%+ |
| **内存使用** | 高（频繁重启） | 低（稳定） | 🚀 显著改善 |
| **稳定性** | 中等 | 优秀 | 🚀 无重启问题 |

## 🔧 启动步骤详解

### 🌟 生产模式启动（推荐新手）

#### 步骤1：构建应用
```bash
npm run build
```
**说明**：这一步会优化和压缩代码，只需要在首次使用或代码更新后执行一次。

**预期输出**：
```
✓ Compiled successfully
✓ Linting and checking validity of types
✓ Collecting page data
✓ Generating static pages (15/15)
✓ Finalizing page optimization
```

#### 步骤2：启动服务器
```bash
npm run start
```
**说明**：启动优化后的生产服务器。

**预期输出**：
```
▲ Next.js 14.2.29
- Local:        http://localhost:1717
✓ Ready in 187ms
```

#### 步骤3：访问应用
打开浏览器访问：`http://localhost:1717`

### 🔧 开发模式启动

#### 步骤1：启动开发服务器
```bash
npm run dev
```
**说明**：启动开发服务器，支持代码热重载。

**预期输出**：
```
▲ Next.js 14.2.29
- Local:        http://localhost:1717
✓ Ready in 2.2s
```

#### 步骤2：等待编译完成
首次访问页面时会进行编译，请耐心等待。

#### 步骤3：访问应用
打开浏览器访问：`http://localhost:1717`

## 🎯 使用建议

### 🌟 **新手推荐：生产模式**

如果您是新手用户，强烈推荐使用生产模式：

✅ **优势**：
- 启动速度极快（187毫秒）
- 页面加载迅速（1-5秒）
- 运行稳定，不会崩溃
- 内存使用少
- 用户体验最佳

❌ **劣势**：
- 修改代码需要重新构建

### 🔧 **开发者选择：开发模式**

如果您需要修改代码或自定义功能：

✅ **优势**：
- 代码修改后自动刷新
- 详细的错误信息
- 便于调试和开发

❌ **劣势**：
- 启动时间长（2-5分钟）
- 页面加载慢（30-60秒）
- 可能出现内存重启

## 🔄 模式切换

### 从开发模式切换到生产模式
```bash
# 1. 停止开发服务器（按 Ctrl+C）
# 2. 构建应用
npm run build
# 3. 启动生产服务器
npm run start
```

### 从生产模式切换到开发模式
```bash
# 1. 停止生产服务器（按 Ctrl+C）
# 2. 启动开发服务器
npm run dev
```

## ❓ 常见问题

### Q: 我应该选择哪种模式？
**A**: 如果您只是使用工具创建数据集，选择**生产模式**。如果您需要修改代码，选择**开发模式**。

### Q: 生产模式会影响功能吗？
**A**: 不会！除了代码热重载外，所有功能完全相同，而且性能更好。

### Q: 为什么开发模式这么慢？
**A**: 开发模式需要实时编译代码，包含调试信息，所以比较慢。生产模式是预编译和优化的。

### Q: 可以随时切换模式吗？
**A**: 可以！停止当前服务器，然后用另一种模式启动即可。

### Q: 构建失败怎么办？
**A**: 检查是否有语法错误，确保所有依赖已安装（`npm install`）。

## 🎉 总结

- **日常使用**：选择生产模式 (`npm run start`)
- **代码开发**：选择开发模式 (`npm run dev`)
- **最佳体验**：生产模式提供最佳性能和稳定性
- **功能完整**：两种模式功能完全相同

现在您可以根据需求选择合适的启动模式，享受 MED-Dataset 的强大功能！
