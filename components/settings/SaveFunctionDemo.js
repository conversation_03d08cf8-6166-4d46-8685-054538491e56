'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Snackbar,
  CircularProgress,
  Chip,
  Grid,
  Divider
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { useTranslation } from 'react-i18next';

/**
 * 保存功能演示组件
 * 展示MED-Dataset项目中任务配置的保存功能
 */
export default function SaveFunctionDemo({ projectId }) {
  const { t } = useTranslation();
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  // 模拟保存功能
  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(false);

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟随机成功/失败
      if (Math.random() > 0.3) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        throw new Error('模拟保存失败');
      }
    } catch (err) {
      setError(err.message);
      setTimeout(() => setError(null), 5000);
    } finally {
      setSaving(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSuccess(false);
    setError(null);
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom align="center" color="primary">
        MED-Dataset 保存功能演示
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            功能特点
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Chip 
                icon={<CheckCircleIcon />} 
                label="实时状态反馈" 
                color="success" 
                variant="outlined" 
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Chip 
                icon={<CheckCircleIcon />} 
                label="数据验证" 
                color="success" 
                variant="outlined" 
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Chip 
                icon={<CheckCircleIcon />} 
                label="错误处理" 
                color="success" 
                variant="outlined" 
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Chip 
                icon={<CheckCircleIcon />} 
                label="用户体验优化" 
                color="success" 
                variant="outlined" 
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            保存功能实现
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            当前的任务配置保存功能包含以下特性：
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body2">
              <strong>加载状态：</strong> 保存时显示加载动画，防止重复提交
            </Typography>
            <Typography component="li" variant="body2">
              <strong>数据验证：</strong> 保存前验证配置参数的有效性
            </Typography>
            <Typography component="li" variant="body2">
              <strong>错误处理：</strong> 详细的错误信息提示和自动隐藏
            </Typography>
            <Typography component="li" variant="body2">
              <strong>成功反馈：</strong> 保存成功后的确认提示
            </Typography>
            <Typography component="li" variant="body2">
              <strong>吸底设计：</strong> 保存按钮固定在页面底部，随时可见
            </Typography>
          </Box>
        </CardContent>
      </Card>

      <Divider sx={{ my: 3 }} />

      {/* 保存按钮演示 */}
      <Box sx={{ textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          保存按钮演示
        </Typography>
        <Button
          variant="contained"
          color="primary"
          size="large"
          startIcon={saving ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            minWidth: '200px',
            py: 1.5,
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: 4
            }
          }}
        >
          {saving ? '保存中...' : '保存任务配置'}
        </Button>
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          点击按钮体验保存功能（模拟70%成功率）
        </Typography>
      </Box>

      {/* 成功提示 */}
      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity="success" 
          sx={{ width: '100%' }}
          icon={<CheckCircleIcon />}
        >
          任务配置保存成功！
        </Alert>
      </Snackbar>

      {/* 错误提示 */}
      <Snackbar
        open={!!error}
        autoHideDuration={5000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity="error" 
          sx={{ width: '100%' }}
          icon={<ErrorIcon />}
        >
          {error}
        </Alert>
      </Snackbar>

      {/* 技术实现说明 */}
      <Card sx={{ mt: 3, bgcolor: 'grey.50' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            技术实现说明
          </Typography>
          <Typography variant="body2" color="text.secondary">
            保存功能通过自定义Hook <code>useTaskSettings</code> 实现，
            包含完整的状态管理、API调用、错误处理和用户反馈机制。
            采用Material-UI组件库确保界面的一致性和可访问性。
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}
