经过详细搜索整个MED-Dataset项目代码库，我确认：

## 搜索结果总结：

1. **已删除的按钮**：
   - ✅ 模型选择按钮（ModelSelect组件）
   - ✅ 任务图标按钮（TaskIcon组件）

2. **未找到用户描述的按钮**：
   - ❌ 未找到全局的问号/帮助按钮
   - ❌ 未找到全局的GitHub图标按钮  
   - ❌ 未找到EN语言切换按钮

3. **搜索覆盖范围**：
   - 全部组件文件
   - 全部页面文件
   - 导航栏组件（Navbar.js）
   - 更新检查器组件（UpdateChecker.js）
   - 全局布局文件（app/layout.js）
   - 固定定位和绝对定位元素

4. **结论**：
   用户看到的问号和GitHub按钮很可能是：
   - 浏览器扩展程序的界面元素
   - 操作系统级别的覆盖层
   - 浏览器本身的UI元素
   - 缓存问题导致的旧版本显示

建议用户在无痕模式下访问应用或禁用浏览器扩展来验证。