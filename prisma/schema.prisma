generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["darwin-arm64", "darwin", "windows", "debian-openssl-3.0.x", "linux-arm64-openssl-3.0.x", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Projects {
  id                   String        @id @default(nanoid(12))
  name                 String
  description          String
  globalPrompt         String        @default("")
  questionPrompt       String        @default("")
  answerPrompt         String        @default("")
  labelPrompt          String        @default("")
  domainTreePrompt     String        @default("")
  defaultModelConfigId String?
  test                 String        @default("")
  createAt             DateTime      @default(now())
  updateAt             DateTime      @updatedAt
  Questions            Questions[]
  Datasets             Datasets[]
  Chunks               Chunks[]
  ModelConfig          ModelConfig[]
  UploadFiles          UploadFiles[]
  Tags                 Tags[]
  Task                 Task[]
  GaPairs              GaPairs[]
}

model UploadFiles {
  id        String    @id @default(nanoid())
  project   Projects  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  fileName  String
  fileExt   String
  path      String
  size      Int
  md5       String
  createAt  DateTime  @default(now())
  updateAt  DateTime  @updatedAt
  GaPairs   GaPairs[]
}

model Chunks {
  id        String      @id @default(nanoid())
  name      String
  project   Projects    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  fileId    String
  fileName  String
  content   String
  summary   String
  size      Int
  createAt  DateTime    @default(now())
  updateAt  DateTime    @updatedAt
  Questions Questions[]

  @@index([projectId])
}

model Tags {
  id        String   @id @default(nanoid())
  label     String
  project   Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  parentId  String?
  parent    Tags?    @relation("Tags", fields: [parentId], references: [id])
  children  Tags[]   @relation("Tags")
}

model Questions {
  id        String   @id @default(nanoid())
  project   Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  chunk     Chunks   @relation(fields: [chunkId], references: [id])
  chunkId   String
  gaPair    GaPairs? @relation(fields: [gaPairId], references: [id])
  gaPairId  String? // Optional: links question to the GA pair that generated it
  question  String
  label     String
  answered  Boolean  @default(false)
  createAt  DateTime @default(now())
  updateAt  DateTime @updatedAt

  @@index([projectId])
}

model Datasets {
  id            String   @id @default(nanoid())
  project       Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId     String
  questionId    String
  question      String
  answer        String
  chunkName     String
  chunkContent  String
  model         String
  questionLabel String
  cot           String
  confirmed     Boolean  @default(false)
  createAt      DateTime @default(now())
  updateAt      DateTime @updatedAt

  @@index([projectId])
}

model LlmProviders {
  id        String      @id
  name      String
  apiUrl    String
  createAt  DateTime    @default(now())
  updateAt  DateTime    @updatedAt
  LlmModels LlmModels[]
}

model LlmModels {
  id         String       @id @default(nanoid())
  modelId    String
  modelName  String
  provider   LlmProviders @relation(fields: [providerId], references: [id])
  providerId String
  createAt   DateTime     @default(now())
  updateAt   DateTime     @updatedAt
}

model ModelConfig {
  id           String   @id @default(nanoid())
  project      Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId    String
  providerId   String
  providerName String
  endpoint     String
  apiKey       String
  modelId      String
  modelName    String
  type         String
  temperature  Float
  maxTokens    Int
  topP         Float
  topK         Float
  status       Int
  createAt     DateTime @default(now())
  updateAt     DateTime @updatedAt
}

model Task {
  id             String    @id @default(nanoid())
  project        Projects  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId      String
  taskType       String // 任务类型: text-processing, question-generation, answer-generation, data-distillation
  status         Int // 任务状态: 0-处理中, 1-已完成, 2-失败, 3-已中断
  startTime      DateTime  @default(now())
  endTime        DateTime?
  completedCount Int       @default(0)
  totalCount     Int       @default(0)
  modelInfo      String // JSON格式存储，包含使用的模型信息
  language       String    @default("zh-CN")
  detail         String    @default("") // 任务详情
  note           String    @default("") // 任务备注
  createAt       DateTime  @default(now())
  updateAt       DateTime  @updatedAt

  @@index([projectId])
}

model GaPairs {
  id            String      @id @default(nanoid())
  project       Projects    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId     String
  uploadFile    UploadFiles @relation(fields: [fileId], references: [id], onDelete: Cascade)
  fileId        String
  pairNumber    Int // 1-5, representing the 5 generated pairs
  genreTitle    String // Genre name/title
  genreDesc     String // Genre description
  audienceTitle String // Audience name/title  
  audienceDesc  String // Audience description
  isActive      Boolean     @default(true) // Whether this pair is active for use
  questions     Questions[] // Questions generated by this GA pair
  createAt      DateTime    @default(now())
  updateAt      DateTime    @updatedAt

  @@unique([fileId, pairNumber])
  @@index([projectId])
  @@index([fileId])
}
