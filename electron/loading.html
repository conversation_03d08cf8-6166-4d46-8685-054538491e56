<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MED-Dataset Loading...</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
          'Helvetica Neue', sans-serif;
        background-color: #f5f5f5;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        padding: 0;
        color: #333;
        overflow: hidden;
      }
      .container {
        text-align: center;
        max-width: 500px;
        padding: 20px;
      }
      .logo {
        width: 120px;
        height: 120px;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
      }
      .title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      .subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 30px;
      }
      .loading {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
      .loading-dot {
        width: 10px;
        height: 10px;
        margin: 0 5px;
        background-color: #1976d2;
        border-radius: 50%;
        animation: loading 1.4s infinite ease-in-out both;
      }
      .loading-dot:nth-child(1) {
        animation-delay: -0.32s;
      }
      .loading-dot:nth-child(2) {
        animation-delay: -0.16s;
      }
      @keyframes loading {
        0%,
        80%,
        100% {
          transform: scale(0);
        }
        40% {
          transform: scale(1);
        }
      }
      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
        100% {
          transform: scale(1);
        }
      }
      .version {
        position: fixed;
        bottom: 20px;
        font-size: 12px;
        color: #999;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img
        src="../public/imgs/logo.png"
        alt="MED-Dataset Logo"
        class="logo"
        onerror="this.src='../public/favicon.ico'; this.style.width='80px'; this.style.height='80px';"
      />
      <h1 class="title">MED-Dataset</h1>
      <p class="subtitle">The first startup may take a bit longer to load. Please be patient. ...</p>
      <div class="loading">
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
      </div>
    </div>
    <div class="version" id="version"></div>

    <script>
      // 获取版本信息
      window.addEventListener('DOMContentLoaded', () => {
        if (window.electronAPI) {
          const version = window.electronAPI.getAppVersion();
          document.getElementById('version').textContent = `版本: v${version}`;
        }
      });
    </script>
  </body>
</html>
