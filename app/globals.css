* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: inherit;
  text-decoration: none;
}

/* 医疗风格渐变文本样式 */
.gradient-text {
  background: linear-gradient(135deg, #0066CC 0%, #00A86B 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* 医疗主题动画 */
@keyframes medicalPulse {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  33% {
    transform: scale(1.03) rotate(120deg);
    opacity: 0.8;
  }
  66% {
    transform: scale(1.06) rotate(240deg);
    opacity: 0.7;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.6;
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes dnaRotate {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

@keyframes medicalFlow {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw);
    opacity: 0;
  }
}

/* 页面容器下间距 */
main {
  min-height: calc(100vh - 64px);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 暗色模式滚动条 */
[data-theme='dark'] ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

[data-theme='dark'] ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 方便的间距类 */
.mt-1 {
  margin-top: 8px;
}
.mt-2 {
  margin-top: 16px;
}
.mt-3 {
  margin-top: 24px;
}
.mt-4 {
  margin-top: 32px;
}
.mb-1 {
  margin-bottom: 8px;
}
.mb-2 {
  margin-bottom: 16px;
}
.mb-3 {
  margin-bottom: 24px;
}
.mb-4 {
  margin-bottom: 32px;
}

/* 响应式样式 */
@media (max-width: 600px) {
  .hide-on-mobile {
    display: none !important;
  }
}

/* 隐藏任何可能的帮助按钮或文档链接 */
[href*="docs.easy-dataset.com"],
[href*="docs."],
.help-button,
.docs-button,
button[title*="帮助"],
button[title*="Help"],
button[title*="文档"],
button[title*="Docs"] {
  display: none !important;
}

/* 为页面内容添加底部边距，避免被footer遮挡 */
body {
  padding-bottom: 60px !important;
}

/* 确保主要内容区域有足够的底部空间 */
main, .main-content, [role="main"] {
  margin-bottom: 60px !important;
}

/* 确保页脚在所有元素之上 */
footer {
  z-index: 9999 !important;
}

/* 输入框和选择框边框简化 */
.plain-select .MuiOutlinedInput-notchedOutline,
.plain-input .MuiOutlinedInput-notchedOutline {
  border-color: transparent !important;
}

/* 卡片悬停效果 */
.hover-card {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.hover-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

[data-theme='dark'] .hover-card:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
}
